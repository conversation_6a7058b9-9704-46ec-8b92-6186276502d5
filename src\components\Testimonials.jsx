import React, { useState, useEffect } from 'react';
import { Star, Quote, ChevronLeft, ChevronRight, Users } from 'lucide-react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'CTO',
      company: 'TechFlow Inc.',
      image: '👩‍💼',
      rating: 5,
      text: 'CodeCraft transformed our legacy system into a modern, scalable platform. Their AI integration increased our efficiency by 300%. The team\'s expertise in cloud architecture is unmatched.',
      project: 'Enterprise Platform Modernization'
    },
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      company: 'StartupVision',
      image: '👨‍💻',
      rating: 5,
      text: 'From concept to launch in just 3 months! CodeCraft built our MVP with cutting-edge technology. Their agile approach and constant communication made the process seamless.',
      project: 'Mobile App Development'
    },
    {
      name: '<PERSON>',
      role: 'Head of Digital',
      company: 'Global Retail Corp',
      image: '👩‍🔬',
      rating: 5,
      text: 'The machine learning solution CodeCraft developed revolutionized our inventory management. We\'ve seen a 40% reduction in waste and 25% increase in customer satisfaction.',
      project: 'AI-Powered Analytics Platform'
    },
    {
      name: '<PERSON>',
      role: 'VP of Engineering',
      company: 'FinanceNext',
      image: '👨‍💼',
      rating: 5,
      text: 'Security was our top priority, and CodeCraft delivered beyond expectations. Their cybersecurity expertise and compliance knowledge helped us achieve SOC 2 certification.',
      project: 'Secure Financial Platform'
    },
    {
      name: 'Lisa Thompson',
      role: 'Product Manager',
      company: 'HealthTech Solutions',
      image: '👩‍⚕️',
      rating: 5,
      text: 'CodeCraft\'s cloud-native architecture scaled perfectly as we grew from 1K to 100K users. Their 24/7 support ensured zero downtime during our critical launch period.',
      project: 'Healthcare Management System'
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000);
    return () => clearInterval(interval);
  }, []);

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        fill={i < rating ? '#f59e0b' : 'none'}
        color={i < rating ? '#f59e0b' : '#d1d5db'}
      />
    ));
  };

  return (
    <section id="testimonials" className="section" style={{ background: 'var(--gray-50)' }}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center" style={{ marginBottom: '4rem' }}>
          <div 
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'var(--gradient-primary)',
              color: 'var(--white)',
              padding: '0.5rem 1rem',
              borderRadius: '2rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              marginBottom: '1rem'
            }}
          >
            <Users size={16} />
            Client Success Stories
          </div>
          
          <h2 style={{ marginBottom: '1rem' }}>
            Trusted by Industry Leaders
          </h2>
          
          <p style={{ maxWidth: '600px', margin: '0 auto', fontSize: '1.125rem' }}>
            Don't just take our word for it. Here's what our clients say about working with CodeCraft 
            and the transformative solutions we've delivered.
          </p>
        </div>

        {/* Main Testimonial */}
        <div 
          style={{
            background: 'var(--white)',
            borderRadius: '1.5rem',
            padding: '3rem',
            boxShadow: 'var(--shadow-xl)',
            border: '1px solid var(--gray-200)',
            marginBottom: '3rem',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Background Quote */}
          <div 
            style={{
              position: 'absolute',
              top: '2rem',
              right: '2rem',
              opacity: 0.1,
              color: 'var(--primary-blue)'
            }}
          >
            <Quote size={80} />
          </div>

          <div style={{ position: 'relative', zIndex: 1 }}>
            {/* Rating */}
            <div style={{ display: 'flex', gap: '0.25rem', marginBottom: '1.5rem' }}>
              {renderStars(testimonials[currentTestimonial].rating)}
            </div>

            {/* Testimonial Text */}
            <blockquote 
              style={{
                fontSize: '1.25rem',
                lineHeight: '1.8',
                color: 'var(--gray-700)',
                marginBottom: '2rem',
                fontStyle: 'italic'
              }}
            >
              "{testimonials[currentTestimonial].text}"
            </blockquote>

            {/* Client Info */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div 
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  background: 'var(--gradient-secondary)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.5rem'
                }}
              >
                {testimonials[currentTestimonial].image}
              </div>
              
              <div>
                <div style={{ fontWeight: '600', color: 'var(--gray-900)', marginBottom: '0.25rem' }}>
                  {testimonials[currentTestimonial].name}
                </div>
                <div style={{ color: 'var(--gray-600)', fontSize: '0.875rem', marginBottom: '0.25rem' }}>
                  {testimonials[currentTestimonial].role} at {testimonials[currentTestimonial].company}
                </div>
                <div 
                  style={{
                    color: 'var(--primary-blue)',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    background: 'var(--gray-100)',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '0.25rem',
                    display: 'inline-block'
                  }}
                >
                  {testimonials[currentTestimonial].project}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div 
            style={{
              position: 'absolute',
              bottom: '2rem',
              right: '2rem',
              display: 'flex',
              gap: '0.5rem'
            }}
          >
            <button
              onClick={prevTestimonial}
              style={{
                background: 'var(--gray-100)',
                border: 'none',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all var(--transition-fast)',
                color: 'var(--gray-600)'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'var(--primary-blue)';
                e.target.style.color = 'var(--white)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'var(--gray-100)';
                e.target.style.color = 'var(--gray-600)';
              }}
            >
              <ChevronLeft size={20} />
            </button>
            
            <button
              onClick={nextTestimonial}
              style={{
                background: 'var(--primary-blue)',
                border: 'none',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all var(--transition-fast)',
                color: 'var(--white)'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'var(--dark-blue)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'var(--primary-blue)';
              }}
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>

        {/* Testimonial Indicators */}
        <div style={{ display: 'flex', justifyContent: 'center', gap: '0.5rem', marginBottom: '3rem' }}>
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentTestimonial(index)}
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                background: index === currentTestimonial ? 'var(--primary-blue)' : 'var(--gray-300)',
                cursor: 'pointer',
                transition: 'all var(--transition-fast)'
              }}
            />
          ))}
        </div>

        {/* Client Logos Grid */}
        <div 
          style={{
            background: 'var(--white)',
            borderRadius: '1rem',
            padding: '2rem',
            boxShadow: 'var(--shadow-md)',
            border: '1px solid var(--gray-200)'
          }}
        >
          <h3 style={{ textAlign: 'center', marginBottom: '2rem', color: 'var(--gray-700)' }}>
            Trusted by 500+ Companies Worldwide
          </h3>
          
          <div 
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
              gap: '2rem',
              alignItems: 'center'
            }}
          >
            {['TechFlow', 'StartupVision', 'Global Retail', 'FinanceNext', 'HealthTech', 'InnovateCorp'].map((company, index) => (
              <div 
                key={index}
                style={{
                  textAlign: 'center',
                  padding: '1rem',
                  color: 'var(--gray-400)',
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  opacity: 0.7,
                  transition: 'opacity var(--transition-fast)',
                  cursor: 'pointer'
                }}
                onMouseEnter={(e) => {
                  e.target.style.opacity = '1';
                  e.target.style.color = 'var(--primary-blue)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.opacity = '0.7';
                  e.target.style.color = 'var(--gray-400)';
                }}
              >
                {company}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
