import React, { useState, useEffect } from 'react';
import { Menu, X, Code, ArrowRight } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <header 
      className={`header ${isScrolled ? 'header-scrolled' : ''}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        background: isScrolled 
          ? 'rgba(255, 255, 255, 0.95)' 
          : 'transparent',
        backdropFilter: isScrolled ? 'blur(10px)' : 'none',
        borderBottom: isScrolled ? '1px solid var(--gray-200)' : 'none',
        transition: 'all var(--transition-normal)',
        padding: '1rem 0'
      }}
    >
      <div className="container">
        <nav className="flex-between">
          {/* Logo */}
          <div 
            className="logo"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              cursor: 'pointer'
            }}
            onClick={() => scrollToSection('hero')}
          >
            <div 
              style={{
                background: 'var(--gradient-primary)',
                borderRadius: '0.5rem',
                padding: '0.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Code size={24} color="white" />
            </div>
            <span 
              style={{
                fontSize: '1.5rem',
                fontWeight: '800',
                color: isScrolled ? 'var(--gray-900)' : 'var(--white)'
              }}
            >
              CodeCraft
            </span>
          </div>

          {/* Desktop Navigation */}
          <div 
            className="nav-links"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '2rem'
            }}
          >
            <div 
              style={{
                display: 'flex',
                gap: '2rem'
              }}
              className="desktop-nav"
            >
              {['Services', 'Technologies', 'Testimonials', 'Contact'].map((item) => (
                <button
                  key={item}
                  onClick={() => scrollToSection(item.toLowerCase())}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: isScrolled ? 'var(--gray-700)' : 'var(--white)',
                    fontSize: '1rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'color var(--transition-fast)',
                    padding: '0.5rem 0'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = 'var(--primary-blue)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = isScrolled ? 'var(--gray-700)' : 'var(--white)';
                  }}
                >
                  {item}
                </button>
              ))}
            </div>

            {/* CTA Button */}
            <button 
              className="btn btn-primary"
              onClick={() => scrollToSection('contact')}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              Get Started
              <ArrowRight size={16} />
            </button>

            {/* Mobile Menu Button */}
            <button
              className="mobile-menu-btn"
              onClick={toggleMenu}
              style={{
                display: 'none',
                background: 'none',
                border: 'none',
                color: isScrolled ? 'var(--gray-700)' : 'var(--white)',
                cursor: 'pointer',
                padding: '0.5rem'
              }}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </nav>

        {/* Mobile Navigation */}
        <div 
          className={`mobile-nav ${isMenuOpen ? 'mobile-nav-open' : ''}`}
          style={{
            display: 'none',
            flexDirection: 'column',
            gap: '1rem',
            marginTop: '1rem',
            padding: '1rem',
            background: 'var(--white)',
            borderRadius: '0.5rem',
            boxShadow: 'var(--shadow-lg)',
            transform: isMenuOpen ? 'translateY(0)' : 'translateY(-10px)',
            opacity: isMenuOpen ? 1 : 0,
            visibility: isMenuOpen ? 'visible' : 'hidden',
            transition: 'all var(--transition-normal)'
          }}
        >
          {['Services', 'Technologies', 'Testimonials', 'Contact'].map((item) => (
            <button
              key={item}
              onClick={() => scrollToSection(item.toLowerCase())}
              style={{
                background: 'none',
                border: 'none',
                color: 'var(--gray-700)',
                fontSize: '1rem',
                fontWeight: '500',
                cursor: 'pointer',
                padding: '0.75rem',
                textAlign: 'left',
                borderRadius: '0.25rem',
                transition: 'background-color var(--transition-fast)'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = 'var(--gray-100)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'transparent';
              }}
            >
              {item}
            </button>
          ))}
        </div>
      </div>

      <style jsx>{`
        @media (max-width: 768px) {
          .desktop-nav {
            display: none !important;
          }
          
          .mobile-menu-btn {
            display: block !important;
          }
          
          .mobile-nav {
            display: flex !important;
          }
        }
      `}</style>
    </header>
  );
};

export default Header;
