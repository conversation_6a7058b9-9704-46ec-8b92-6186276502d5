import React from 'react';
import { Code2, Database, Cloud, Smartphone, Cpu, Shield } from 'lucide-react';

const Technologies = () => {
  const techCategories = [
    {
      icon: <Code2 size={32} />,
      title: 'Frontend',
      color: '#3b82f6',
      technologies: [
        { name: 'React', level: 95 },
        { name: 'Vue.js', level: 90 },
        { name: 'Angular', level: 85 },
        { name: 'TypeScript', level: 92 },
        { name: 'Next.js', level: 88 }
      ]
    },
    {
      icon: <Database size={32} />,
      title: 'Backend',
      color: '#10b981',
      technologies: [
        { name: 'Node.js', level: 95 },
        { name: 'Python', level: 92 },
        { name: 'Java', level: 88 },
        { name: 'Go', level: 85 },
        { name: '.NET', level: 82 }
      ]
    },
    {
      icon: <Cloud size={32} />,
      title: 'Cloud & DevOps',
      color: '#8b5cf6',
      technologies: [
        { name: 'A<PERSON>', level: 93 },
        { name: 'Azure', level: 88 },
        { name: 'Docker', level: 95 },
        { name: 'Kubernetes', level: 90 },
        { name: 'Terraform', level: 85 }
      ]
    },
    {
      icon: <Smartphone size={32} />,
      title: 'Mobile',
      color: '#f59e0b',
      technologies: [
        { name: 'React Native', level: 92 },
        { name: 'Flutter', level: 88 },
        { name: 'iOS (Swift)', level: 85 },
        { name: 'Android (Kotlin)', level: 87 },
        { name: 'Ionic', level: 80 }
      ]
    },
    {
      icon: <Cpu size={32} />,
      title: 'AI & Data',
      color: '#ef4444',
      technologies: [
        { name: 'TensorFlow', level: 90 },
        { name: 'PyTorch', level: 88 },
        { name: 'OpenAI GPT', level: 92 },
        { name: 'Apache Spark', level: 85 },
        { name: 'Elasticsearch', level: 87 }
      ]
    },
    {
      icon: <Shield size={32} />,
      title: 'Security',
      color: '#dc2626',
      technologies: [
        { name: 'OAuth 2.0', level: 95 },
        { name: 'JWT', level: 92 },
        { name: 'SSL/TLS', level: 90 },
        { name: 'OWASP', level: 88 },
        { name: 'Penetration Testing', level: 85 }
      ]
    }
  ];

  const stats = [
    { number: '500+', label: 'Projects Delivered' },
    { number: '50+', label: 'Technologies Mastered' },
    { number: '99.9%', label: 'Uptime Guarantee' },
    { number: '24/7', label: 'Support Available' }
  ];

  return (
    <section id="technologies" className="section">
      <div className="container">
        {/* Section Header */}
        <div className="text-center" style={{ marginBottom: '4rem' }}>
          <div 
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'var(--gradient-secondary)',
              color: 'var(--white)',
              padding: '0.5rem 1rem',
              borderRadius: '2rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              marginBottom: '1rem'
            }}
          >
            <Cpu size={16} />
            Technologies
          </div>
          
          <h2 style={{ marginBottom: '1rem' }}>
            Cutting-Edge Technology Stack
          </h2>
          
          <p style={{ maxWidth: '600px', margin: '0 auto', fontSize: '1.125rem' }}>
            We leverage the latest technologies and frameworks to build robust, scalable, 
            and future-proof solutions that give you a competitive edge.
          </p>
        </div>

        {/* Stats */}
        <div 
          className="grid grid-4" 
          style={{ 
            marginBottom: '4rem',
            gap: '2rem'
          }}
        >
          {stats.map((stat, index) => (
            <div 
              key={index}
              style={{
                textAlign: 'center',
                padding: '2rem 1rem',
                background: 'var(--white)',
                borderRadius: '1rem',
                boxShadow: 'var(--shadow-md)',
                border: '1px solid var(--gray-200)'
              }}
            >
              <div 
                style={{
                  fontSize: '2.5rem',
                  fontWeight: '800',
                  background: 'var(--gradient-primary)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  marginBottom: '0.5rem'
                }}
              >
                {stat.number}
              </div>
              <div style={{ color: 'var(--gray-600)', fontWeight: '500' }}>
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Technologies Grid */}
        <div className="grid grid-3" style={{ gap: '2rem' }}>
          {techCategories.map((category, index) => (
            <div
              key={index}
              style={{
                background: 'var(--white)',
                borderRadius: '1rem',
                padding: '2rem',
                boxShadow: 'var(--shadow-md)',
                border: '1px solid var(--gray-200)',
                transition: 'all var(--transition-normal)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = 'var(--shadow-lg)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'var(--shadow-md)';
              }}
            >
              {/* Category Header */}
              <div style={{ marginBottom: '2rem' }}>
                <div 
                  style={{
                    background: category.color,
                    borderRadius: '0.75rem',
                    padding: '1rem',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: '1rem',
                    color: 'var(--white)'
                  }}
                >
                  {category.icon}
                </div>
                <h3 style={{ color: 'var(--gray-900)' }}>
                  {category.title}
                </h3>
              </div>

              {/* Technologies List */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {category.technologies.map((tech, techIndex) => (
                  <div key={techIndex}>
                    <div 
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '0.5rem'
                      }}
                    >
                      <span style={{ fontWeight: '500', color: 'var(--gray-700)' }}>
                        {tech.name}
                      </span>
                      <span style={{ fontSize: '0.875rem', color: 'var(--gray-500)' }}>
                        {tech.level}%
                      </span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div 
                      style={{
                        width: '100%',
                        height: '6px',
                        background: 'var(--gray-200)',
                        borderRadius: '3px',
                        overflow: 'hidden'
                      }}
                    >
                      <div 
                        style={{
                          width: `${tech.level}%`,
                          height: '100%',
                          background: category.color,
                          borderRadius: '3px',
                          transition: 'width 1s ease-in-out'
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div 
          style={{
            marginTop: '4rem',
            textAlign: 'center',
            padding: '3rem',
            background: 'var(--gray-900)',
            borderRadius: '1rem',
            color: 'var(--white)',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Background Pattern */}
          <div 
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
              `,
              pointerEvents: 'none'
            }}
          />

          <div style={{ position: 'relative', zIndex: 1 }}>
            <h3 style={{ marginBottom: '1rem', color: 'var(--white)' }}>
              Always Learning, Always Innovating
            </h3>
            <p style={{ 
              marginBottom: '2rem', 
              color: 'rgba(255, 255, 255, 0.8)', 
              maxWidth: '600px', 
              margin: '0 auto 2rem' 
            }}>
              Our team stays at the forefront of technology trends, continuously learning and 
              adopting new tools to deliver the best solutions for our clients.
            </p>
            
            <div 
              style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '2rem',
                flexWrap: 'wrap'
              }}
            >
              {['Continuous Learning', 'Industry Certifications', 'Open Source Contributions', 'Tech Community'].map((item, index) => (
                <div 
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    color: 'rgba(255, 255, 255, 0.9)',
                    fontSize: '0.875rem'
                  }}
                >
                  <div 
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      background: 'var(--light-blue)'
                    }}
                  />
                  {item}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Technologies;
