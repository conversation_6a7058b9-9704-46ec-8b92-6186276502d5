import React from 'react';
import { 
  Code, 
  Mail, 
  Phone, 
  MapPin, 
  Github, 
  Linkedin, 
  Twitter,
  ArrowRight,
  Send
} from 'lucide-react';

const Footer = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer id="contact" style={{ background: 'var(--gray-900)', color: 'var(--white)' }}>
      {/* Main Footer Content */}
      <div className="container" style={{ paddingTop: '4rem', paddingBottom: '2rem' }}>
        <div 
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '3rem',
            marginBottom: '3rem'
          }}
        >
          {/* Company Info */}
          <div>
            <div 
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '1.5rem'
              }}
            >
              <div 
                style={{
                  background: 'var(--gradient-primary)',
                  borderRadius: '0.5rem',
                  padding: '0.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Code size={24} color="white" />
              </div>
              <span style={{ fontSize: '1.5rem', fontWeight: '800' }}>
                CodeCraft
              </span>
            </div>
            
            <p style={{ 
              color: 'rgba(255, 255, 255, 0.8)', 
              marginBottom: '2rem',
              lineHeight: '1.6'
            }}>
              Transforming ideas into powerful software solutions. We build the future with 
              cutting-edge technology, innovative design, and unmatched expertise.
            </p>

            {/* Social Links */}
            <div style={{ display: 'flex', gap: '1rem' }}>
              {[
                { icon: <Github size={20} />, href: '#', label: 'GitHub' },
                { icon: <Linkedin size={20} />, href: '#', label: 'LinkedIn' },
                { icon: <Twitter size={20} />, href: '#', label: 'Twitter' }
              ].map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  aria-label={social.label}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '40px',
                    height: '40px',
                    background: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '50%',
                    color: 'var(--white)',
                    transition: 'all var(--transition-fast)',
                    textDecoration: 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'var(--primary-blue)';
                    e.target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                    e.target.style.transform = 'translateY(0)';
                  }}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 style={{ marginBottom: '1.5rem', color: 'var(--white)' }}>
              Quick Links
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {[
                { label: 'Services', id: 'services' },
                { label: 'Technologies', id: 'technologies' },
                { label: 'Testimonials', id: 'testimonials' },
                { label: 'About Us', id: 'hero' },
                { label: 'Contact', id: 'contact' }
              ].map((link, index) => (
                <button
                  key={index}
                  onClick={() => scrollToSection(link.id)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1rem',
                    cursor: 'pointer',
                    textAlign: 'left',
                    padding: '0.25rem 0',
                    transition: 'color var(--transition-fast)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = 'var(--light-blue)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = 'rgba(255, 255, 255, 0.8)';
                  }}
                >
                  {link.label}
                </button>
              ))}
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 style={{ marginBottom: '1.5rem', color: 'var(--white)' }}>
              Our Services
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
              {[
                'AI & Machine Learning',
                'Cloud Solutions',
                'Custom Software',
                'Mobile Development',
                'Data Engineering',
                'Cybersecurity'
              ].map((service, index) => (
                <div
                  key={index}
                  style={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1rem',
                    padding: '0.25rem 0',
                    cursor: 'pointer',
                    transition: 'color var(--transition-fast)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = 'var(--light-blue)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = 'rgba(255, 255, 255, 0.8)';
                  }}
                >
                  {service}
                </div>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h4 style={{ marginBottom: '1.5rem', color: 'var(--white)' }}>
              Get In Touch
            </h4>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginBottom: '2rem' }}>
              {[
                { icon: <Mail size={18} />, text: '<EMAIL>' },
                { icon: <Phone size={18} />, text: '+****************' },
                { icon: <MapPin size={18} />, text: 'San Francisco, CA' }
              ].map((contact, index) => (
                <div 
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    color: 'rgba(255, 255, 255, 0.8)'
                  }}
                >
                  <div style={{ color: 'var(--light-blue)' }}>
                    {contact.icon}
                  </div>
                  <span>{contact.text}</span>
                </div>
              ))}
            </div>

            {/* Newsletter Signup */}
            <div>
              <h5 style={{ marginBottom: '1rem', color: 'var(--white)', fontSize: '1rem' }}>
                Stay Updated
              </h5>
              <div 
                style={{
                  display: 'flex',
                  gap: '0.5rem',
                  background: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '0.5rem',
                  padding: '0.5rem'
                }}
              >
                <input
                  type="email"
                  placeholder="Enter your email"
                  style={{
                    flex: 1,
                    background: 'transparent',
                    border: 'none',
                    color: 'var(--white)',
                    fontSize: '0.875rem',
                    padding: '0.5rem',
                    outline: 'none'
                  }}
                />
                <button
                  style={{
                    background: 'var(--primary-blue)',
                    border: 'none',
                    borderRadius: '0.25rem',
                    padding: '0.5rem',
                    color: 'var(--white)',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background var(--transition-fast)'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'var(--dark-blue)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'var(--primary-blue)';
                  }}
                >
                  <Send size={16} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div 
          style={{
            background: 'var(--gradient-primary)',
            borderRadius: '1rem',
            padding: '3rem',
            textAlign: 'center',
            marginBottom: '3rem'
          }}
        >
          <h3 style={{ marginBottom: '1rem', color: 'var(--white)' }}>
            Ready to Start Your Next Project?
          </h3>
          <p style={{ 
            marginBottom: '2rem', 
            color: 'rgba(255, 255, 255, 0.9)',
            maxWidth: '500px',
            margin: '0 auto 2rem'
          }}>
            Let's discuss how we can help transform your ideas into powerful software solutions.
          </p>
          <button 
            className="btn"
            style={{
              background: 'var(--white)',
              color: 'var(--primary-blue)',
              fontWeight: '600',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
            onClick={() => scrollToSection('hero')}
          >
            Get Started Today
            <ArrowRight size={16} />
          </button>
        </div>

        {/* Bottom Bar */}
        <div 
          style={{
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            paddingTop: '2rem',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: '1rem'
          }}
        >
          <div style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '0.875rem' }}>
            © 2024 CodeCraft. All rights reserved.
          </div>
          
          <div style={{ display: 'flex', gap: '2rem' }}>
            {['Privacy Policy', 'Terms of Service', 'Cookie Policy'].map((link, index) => (
              <a
                key={index}
                href="#"
                style={{
                  color: 'rgba(255, 255, 255, 0.6)',
                  fontSize: '0.875rem',
                  textDecoration: 'none',
                  transition: 'color var(--transition-fast)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = 'var(--light-blue)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = 'rgba(255, 255, 255, 0.6)';
                }}
              >
                {link}
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
