import React from 'react';
import { ArrowRight, Play, CheckCircle } from 'lucide-react';

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section 
      id="hero"
      style={{
        background: 'var(--gradient-hero)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* Background Pattern */}
      <div 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
          `,
          pointerEvents: 'none'
        }}
      />

      <div className="container" style={{ position: 'relative', zIndex: 1 }}>
        <div 
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '4rem',
            alignItems: 'center',
            minHeight: '80vh'
          }}
          className="hero-grid"
        >
          {/* Left Column - Content */}
          <div style={{ color: 'var(--white)' }}>
            {/* Badge */}
            <div 
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                padding: '0.5rem 1rem',
                borderRadius: '2rem',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                marginBottom: '2rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              <CheckCircle size={16} color="#10b981" />
              Trusted by 500+ Companies
            </div>

            {/* Main Heading */}
            <h1 
              style={{
                fontSize: '4rem',
                fontWeight: '800',
                lineHeight: '1.1',
                marginBottom: '1.5rem',
                background: 'linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Build the Future with
              <span 
                style={{
                  display: 'block',
                  background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Cutting-Edge Code
              </span>
            </h1>

            {/* Subtitle */}
            <p 
              style={{
                fontSize: '1.25rem',
                lineHeight: '1.7',
                color: 'rgba(255, 255, 255, 0.8)',
                marginBottom: '2rem',
                maxWidth: '500px'
              }}
            >
              We transform your ideas into powerful, scalable software solutions using the latest technologies. From AI-powered applications to cloud-native platforms.
            </p>

            {/* Key Points */}
            <div 
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '0.75rem',
                marginBottom: '3rem'
              }}
            >
              {[
                'AI & Machine Learning Integration',
                'Cloud-Native Architecture',
                'Enterprise-Grade Security',
                '24/7 Support & Maintenance'
              ].map((point, index) => (
                <div 
                  key={index}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    color: 'rgba(255, 255, 255, 0.9)'
                  }}
                >
                  <CheckCircle size={20} color="#10b981" />
                  <span>{point}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div 
              style={{
                display: 'flex',
                gap: '1rem',
                alignItems: 'center'
              }}
              className="hero-buttons"
            >
              <button 
                className="btn btn-primary btn-lg"
                onClick={() => scrollToSection('contact')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
              >
                Start Your Project
                <ArrowRight size={20} />
              </button>

              <button 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'var(--white)',
                  padding: '1rem 2rem',
                  borderRadius: '0.5rem',
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all var(--transition-fast)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'rgba(255, 255, 255, 0.2)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                }}
              >
                <Play size={20} />
                Watch Demo
              </button>
            </div>
          </div>

          {/* Right Column - Visual */}
          <div 
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative'
            }}
          >
            {/* Code Window Mockup */}
            <div 
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '1rem',
                padding: '1.5rem',
                width: '100%',
                maxWidth: '500px',
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
              }}
            >
              {/* Window Header */}
              <div 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '1rem',
                  paddingBottom: '1rem',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
                }}
              >
                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#ef4444' }} />
                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#f59e0b' }} />
                <div style={{ width: '12px', height: '12px', borderRadius: '50%', background: '#10b981' }} />
                <span style={{ marginLeft: '1rem', color: 'rgba(255, 255, 255, 0.7)', fontSize: '0.875rem' }}>
                  app.js
                </span>
              </div>

              {/* Code Content */}
              <div 
                style={{
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '0.875rem',
                  lineHeight: '1.6',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}
              >
                <div style={{ color: '#8b5cf6' }}>import</div>
                <div style={{ color: '#3b82f6', marginLeft: '1rem' }}>AI, Cloud, Security</div>
                <div style={{ color: '#8b5cf6', marginTop: '1rem' }}>from</div>
                <div style={{ color: '#10b981', marginLeft: '1rem' }}>'@codecraft/platform'</div>
                <div style={{ marginTop: '1.5rem', color: '#f59e0b' }}>
                  const <span style={{ color: '#e2e8f0' }}>solution</span> = 
                </div>
                <div style={{ color: '#3b82f6', marginLeft: '1rem' }}>
                  buildApp({'{'}
                </div>
                <div style={{ marginLeft: '2rem', color: '#e2e8f0' }}>
                  ai: <span style={{ color: '#10b981' }}>true</span>,
                </div>
                <div style={{ marginLeft: '2rem', color: '#e2e8f0' }}>
                  cloud: <span style={{ color: '#10b981' }}>true</span>,
                </div>
                <div style={{ marginLeft: '2rem', color: '#e2e8f0' }}>
                  scale: <span style={{ color: '#f59e0b' }}>'infinite'</span>
                </div>
                <div style={{ color: '#3b82f6', marginLeft: '1rem' }}>{'}'});</div>
              </div>
            </div>

            {/* Floating Elements */}
            <div 
              style={{
                position: 'absolute',
                top: '10%',
                right: '10%',
                background: 'rgba(59, 130, 246, 0.2)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '0.5rem',
                padding: '0.75rem',
                fontSize: '0.875rem',
                color: 'var(--white)',
                animation: 'float 3s ease-in-out infinite'
              }}
            >
              ⚡ Real-time
            </div>

            <div 
              style={{
                position: 'absolute',
                bottom: '20%',
                left: '5%',
                background: 'rgba(139, 92, 246, 0.2)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                borderRadius: '0.5rem',
                padding: '0.75rem',
                fontSize: '0.875rem',
                color: 'var(--white)',
                animation: 'float 3s ease-in-out infinite 1s'
              }}
            >
              🚀 Scalable
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
          .hero-grid {
            grid-template-columns: 1fr !important;
            gap: 2rem !important;
            text-align: center;
          }
          
          .hero-buttons {
            flex-direction: column !important;
            align-items: stretch !important;
          }
        }
      `}</style>
    </section>
  );
};

export default Hero;
