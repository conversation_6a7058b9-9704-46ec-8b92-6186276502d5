import React from 'react';
import { 
  Code, 
  Cloud, 
  Smartphone, 
  Database, 
  Shield, 
  Zap,
  Brain,
  Globe,
  ArrowRight
} from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: <Brain size={32} />,
      title: 'AI & Machine Learning',
      description: 'Harness the power of artificial intelligence to automate processes, gain insights, and create intelligent applications that learn and adapt.',
      features: ['Custom AI Models', 'Natural Language Processing', 'Computer Vision', 'Predictive Analytics']
    },
    {
      icon: <Cloud size={32} />,
      title: 'Cloud Solutions',
      description: 'Build scalable, resilient cloud-native applications with modern architecture patterns and best practices for performance and security.',
      features: ['AWS/Azure/GCP', 'Microservices', 'Serverless', 'DevOps & CI/CD']
    },
    {
      icon: <Code size={32} />,
      title: 'Custom Software Development',
      description: 'End-to-end software development services tailored to your business needs, from concept to deployment and maintenance.',
      features: ['Web Applications', 'Enterprise Software', 'API Development', 'Legacy Modernization']
    },
    {
      icon: <Smartphone size={32} />,
      title: 'Mobile Development',
      description: 'Create engaging mobile experiences with native and cross-platform solutions that deliver exceptional user experiences.',
      features: ['iOS & Android', 'React Native', 'Flutter', 'Progressive Web Apps']
    },
    {
      icon: <Database size={32} />,
      title: 'Data Engineering',
      description: 'Transform your data into actionable insights with robust data pipelines, analytics platforms, and business intelligence solutions.',
      features: ['Data Pipelines', 'Real-time Analytics', 'Data Warehousing', 'Business Intelligence']
    },
    {
      icon: <Shield size={32} />,
      title: 'Cybersecurity',
      description: 'Protect your digital assets with comprehensive security solutions, from secure coding practices to advanced threat detection.',
      features: ['Security Audits', 'Penetration Testing', 'Compliance', 'Threat Monitoring']
    }
  ];

  return (
    <section id="services" className="section" style={{ background: 'var(--gray-50)' }}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center" style={{ marginBottom: '4rem' }}>
          <div 
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem',
              background: 'var(--gradient-primary)',
              color: 'var(--white)',
              padding: '0.5rem 1rem',
              borderRadius: '2rem',
              fontSize: '0.875rem',
              fontWeight: '600',
              marginBottom: '1rem'
            }}
          >
            <Zap size={16} />
            Our Services
          </div>
          
          <h2 style={{ marginBottom: '1rem' }}>
            Comprehensive Software Solutions
          </h2>
          
          <p style={{ maxWidth: '600px', margin: '0 auto', fontSize: '1.125rem' }}>
            From AI-powered applications to cloud-native platforms, we deliver cutting-edge solutions 
            that drive innovation and accelerate your business growth.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-3" style={{ gap: '2rem' }}>
          {services.map((service, index) => (
            <div
              key={index}
              style={{
                background: 'var(--white)',
                borderRadius: '1rem',
                padding: '2rem',
                boxShadow: 'var(--shadow-md)',
                border: '1px solid var(--gray-200)',
                transition: 'all var(--transition-normal)',
                cursor: 'pointer',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-8px)';
                e.currentTarget.style.boxShadow = 'var(--shadow-xl)';
                e.currentTarget.style.borderColor = 'var(--primary-blue)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'var(--shadow-md)';
                e.currentTarget.style.borderColor = 'var(--gray-200)';
              }}
            >
              {/* Background Gradient */}
              <div 
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: 'var(--gradient-primary)'
                }}
              />

              {/* Icon */}
              <div 
                style={{
                  background: 'var(--gradient-secondary)',
                  borderRadius: '0.75rem',
                  padding: '1rem',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '1.5rem',
                  color: 'var(--white)'
                }}
              >
                {service.icon}
              </div>

              {/* Content */}
              <h3 style={{ marginBottom: '1rem', color: 'var(--gray-900)' }}>
                {service.title}
              </h3>

              <p style={{ marginBottom: '1.5rem', color: 'var(--gray-600)' }}>
                {service.description}
              </p>

              {/* Features */}
              <div style={{ marginBottom: '1.5rem' }}>
                {service.features.map((feature, featureIndex) => (
                  <div 
                    key={featureIndex}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      marginBottom: '0.5rem',
                      fontSize: '0.875rem',
                      color: 'var(--gray-600)'
                    }}
                  >
                    <div 
                      style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        background: 'var(--primary-blue)'
                      }}
                    />
                    {feature}
                  </div>
                ))}
              </div>

              {/* Learn More Link */}
              <div 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: 'var(--primary-blue)',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'gap var(--transition-fast)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.gap = '0.75rem';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.gap = '0.5rem';
                }}
              >
                Learn More
                <ArrowRight size={16} />
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div 
          style={{
            textAlign: 'center',
            marginTop: '4rem',
            padding: '3rem',
            background: 'var(--gradient-primary)',
            borderRadius: '1rem',
            color: 'var(--white)'
          }}
        >
          <Globe size={48} style={{ marginBottom: '1rem', opacity: 0.8 }} />
          <h3 style={{ marginBottom: '1rem', color: 'var(--white)' }}>
            Ready to Transform Your Business?
          </h3>
          <p style={{ marginBottom: '2rem', color: 'rgba(255, 255, 255, 0.9)', maxWidth: '500px', margin: '0 auto 2rem' }}>
            Let's discuss how our expertise can help you build innovative solutions that drive growth and success.
          </p>
          <button 
            className="btn"
            style={{
              background: 'var(--white)',
              color: 'var(--primary-blue)',
              fontWeight: '600'
            }}
            onClick={() => {
              const element = document.getElementById('contact');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
          >
            Start Your Project Today
            <ArrowRight size={16} />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Services;
